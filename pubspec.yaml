name: my_portfolio_web
description: Senior Flut<PERSON> Developer & Mobile Architect with 6+ years of experience in cross-platform development.
version: 1.0.0+1
publish_to: none

environment:
  sdk: ">=3.3.0 <4.0.0"

dependencies:
  animated_text_kit: ^4.2.2
  bloc: ^9.0.0
  dio: ^5.4.1
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.1
  flutter_carousel_widget: ^3.1.0
  flutter_localizations:
    sdk: flutter
  flutter_native_splash: ^2.3.10
  flutter_svg: ^2.0.10+1
  font_awesome_flutter: ^10.7.0
  get: ^4.6.6
  google_fonts: ^6.1.0
  intl: ^0.19.0
  lottie: ^3.0.0
  open_file: ^3.3.2
  path_provider: ^2.1.2
  permission_handler: ^12.0.0+1
  timeline_tile: ^2.0.0
  url_launcher: ^6.2.4

dev_dependencies:
  bloc_test: ^10.0.0
  build_runner: ^2.4.15
  flutter_launcher_icons: ^0.14.3
  flutter_test:
    sdk: flutter
  mockito: ^5.4.6
  mocktail: ^1.0.4
  very_good_analysis: ^7.0.0

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/cv/
    - assets/images/
    - assets/fonts/
    - assets/icons/
    - assets/tile_images/
    - assets/tech_icon_svg/
    - assets/animations/

  # Using system fonts instead of local font files
  # fonts:
  #   - family: Montserrat
  #     fonts:
  #       - asset: assets/fonts/Montserrat-Regular.ttf
  #         weight: 400
  #       - asset: assets/fonts/Montserrat-Bold.ttf
  #         weight: 700
